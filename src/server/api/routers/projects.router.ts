import { FILTER_TAKE, PROJECT_STATUS } from "@/libs/constants";
import { filterSchema } from "@/schemas/api.schemas";
import {
  ProjectCreateSchema,
  ProjectUpdateSchema,
} from "@/schemas/project.schemas";
import { getTodayStartAndEnd } from "@/utils/dates"; // <-- added import
import { omit } from "@/utils/omit";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// Utility function to compute dynamic project status
const computeProjectStatus = (project: any) => {
  const { todayStart, todayEnd } = getTodayStartAndEnd();

  if (
    project.status === PROJECT_STATUS.APPROVED &&
    project.startDate &&
    project.endDate
  ) {
    const startDate = new Date(project.startDate);
    const endDate = new Date(project.endDate);

    // Check if project is completed (past end date)
    if (endDate < todayStart) {
      return { ...project, status: PROJECT_STATUS.COMPLETED };
    }

    // Check if project is currently active (within date range)
    if (startDate <= todayEnd && endDate >= todayStart) {
      return { ...project, status: PROJECT_STATUS.ACTIVE };
    }

    // Project is approved but not yet started or no dates set
    return project;
  }

  return project;
};

// Utility function to compute status for an array of projects
const computeProjectsStatus = (projects: any[]) => {
  return projects.map(computeProjectStatus);
};

export const projectsRouter = createTRPCRouter({
  create: protectedProcedure
    .input(z.object({ organizationId: z.string() }).merge(ProjectCreateSchema))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.project.create({
        data: input,
      });
    }),
  getAll: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        status: z.string().optional(),
        ...filterSchema,
      }),
    )
    .query(async ({ ctx, input }) => {
      const take = input?.take || FILTER_TAKE;

      // If filtering for approved projects only, we need to handle this at the database level
      // to avoid the dynamic status computation converting them to active
      if (input.status === PROJECT_STATUS.APPROVED) {
        const { todayStart, todayEnd } = getTodayStartAndEnd();

        const data = await ctx.db.project.findMany({
          where: {
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            name: {
              contains: input?.searchString,
            },
            // Only include projects that are not currently active
            AND: [
              {
                OR: [
                  // Projects with no start date
                  { startDate: null },
                  // Projects not yet started
                  { startDate: { gt: todayEnd } },
                ],
              },
              {
                OR: [
                  // Projects with no end date
                  { endDate: null },
                  // Projects not yet ended (for future projects)
                  { endDate: { gte: todayStart } },
                ],
              },
            ],
          },
          include: {
            _count: {
              select: {
                slides: true,
              },
            },
          },
          ...(input.cursor && {
            cursor: {
              id: input.cursor,
            },
            skip: 1,
          }),
          take,
          orderBy: { createdAt: "desc" },
        });

        // Return approved projects without status computation
        const result = { data, cursor: "" };
        if (data.length < take) return result;
        return { ...result, cursor: data.at(-1)?.id || "" };
      }

      if (input.status === PROJECT_STATUS.COMPLETED) {
        const { todayStart } = getTodayStartAndEnd();

        const data = await ctx.db.project.findMany({
          where: {
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            name: {
              contains: input?.searchString,
            },
            // Only projects that are past their end date
            endDate: { lt: todayStart },
          },
          include: {
            _count: {
              select: {
                slides: true,
              },
            },
          },
          ...(input.cursor && {
            cursor: {
              id: input.cursor,
            },
            skip: 1,
          }),
          take,
          orderBy: { createdAt: "desc" },
        });

        // Apply status computation to convert to COMPLETED
        const processedData = computeProjectsStatus(data);
        const result = { data: processedData, cursor: "" };
        if (processedData.length < take) return result;
        return { ...result, cursor: processedData.at(-1)?.id || "" };
      }

      // For all other status filters, use the existing logic
      const data = await ctx.db.project.findMany({
        where: {
          organizationId: input.organizationId,
          name: {
            contains: input?.searchString,
          },
          ...(input.status !== "all" &&
            input.status !== PROJECT_STATUS.ACTIVE && { status: input.status }),
        },
        include: {
          _count: {
            select: {
              slides: true,
            },
          },
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      // Compute dynamic status for all projects
      let processedData = computeProjectsStatus(data);

      // Filter by active status if requested
      if (input.status === PROJECT_STATUS.ACTIVE) {
        processedData = processedData.filter(
          (project) => project.status === PROJECT_STATUS.ACTIVE,
        );
      }

      const result = { data: processedData, cursor: "" };

      if (processedData.length < take) return result;

      return { ...result, cursor: processedData.at(-1)?.id || "" };
    }),
  getAllActive: protectedProcedure
    .input(z.object({ organizationId: z.string(), ...filterSchema }))
    .query(async ({ ctx, input }) => {
      const take = input?.take || FILTER_TAKE;
      const { todayStart, todayEnd } = getTodayStartAndEnd();

      const data = await ctx.db.project.findMany({
        where: {
          organizationId: input.organizationId,
          status: PROJECT_STATUS.APPROVED, // Only approved projects
          startDate: {
            lte: todayEnd, // Start date is on or before todayEnd
          },
          endDate: {
            gte: todayStart, // End date is on or after todayStart
          },
          name: {
            contains: input?.searchString,
          },
        },
        include: {
          _count: {
            select: {
              slides: true,
            },
          },
          locations: {
            select: {
              id: true,
              name: true,
              city: true,
              state: true,
            },
          },
          sublocations: {
            select: {
              id: true,
              name: true,
              locationId: true,
            },
          },
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      // Convert approved projects within date range to active status
      const processedData = data.map((project) => ({
        ...project,
        status: PROJECT_STATUS.ACTIVE,
      }));

      const result = { data: processedData, cursor: "" };
      if (processedData.length < take) return result;
      return { ...result, cursor: processedData.at(-1)?.id || "" };
    }),
  getAllApprovedActiveAndCompleted: protectedProcedure
    .input(z.object({ organizationId: z.string(), ...filterSchema }))
    .query(async ({ ctx, input }) => {
      const take = input?.take || FILTER_TAKE;

      const data = await ctx.db.project.findMany({
        where: {
          organizationId: input.organizationId,
          status: PROJECT_STATUS.APPROVED, // Get all approved projects (regardless of dates)
          name: {
            contains: input?.searchString,
          },
        },
        include: {
          _count: {
            select: {
              slides: true,
            },
          },
          locations: {
            select: {
              id: true,
              name: true,
              city: true,
              state: true,
            },
          },
          sublocations: {
            select: {
              id: true,
              name: true,
              locationId: true,
            },
          },
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      // Compute dynamic status for all approved projects (some will become active, some completed)
      const processedData = computeProjectsStatus(data);

      const result = { data: processedData, cursor: "" };
      if (processedData.length < take) return result;
      return { ...result, cursor: processedData.at(-1)?.id || "" };
    }),
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const data = await ctx.db.project.findUnique({
        where: { id: input.id },
        include: {
          _count: {
            select: {
              slides: true,
            },
          },
        },
      });

      if (!data) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      // Compute dynamic status
      return computeProjectStatus(data);
    }),
  getRecentProjects: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const data = await ctx.db.project.findMany({
        where: {
          organizationId: input.organizationId,
          updatedAt: { gt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
        },
        include: {
          _count: {
            select: {
              slides: true,
            },
          },
        },
        take: 4,
        orderBy: { createdAt: "desc" },
      });

      // Compute dynamic status for recent projects
      return computeProjectsStatus(data);
    }),
  getMetrics: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const [teamMembers, activeProjects, totalProjects, recentProjects] =
        await Promise.all([
          ctx.db.member.count({
            where: {
              organizationId: input.organizationId,
            },
          }),
          ctx.db.project.count({
            where: {
              organizationId: input.organizationId,
              status: PROJECT_STATUS.EDITING,
            },
          }),
          ctx.db.project.count({
            where: {
              organizationId: input.organizationId,
            },
          }),
          ctx.db.project.findMany({
            where: {
              organizationId: input.organizationId,
              updatedAt: { gt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
            },
            select: {
              id: true,
              name: true,
              status: true,
              updatedAt: true,
              startDate: true,
              endDate: true,
              _count: {
                select: {
                  slides: true,
                },
              },
            },
            take: 4,
            orderBy: { createdAt: "desc" },
          }),
        ]);

      return {
        teamMembers,
        activeProjects,
        totalProjects,
        recentProjects: computeProjectsStatus(recentProjects),
      };
    }),
  updateById: protectedProcedure
    .input(z.object({ id: z.string() }).merge(ProjectUpdateSchema))
    .mutation(async ({ ctx, input }) => {
      const updatedProject = await ctx.db.project.update({
        where: { id: input.id },
        data: { ...omit(input, ["id"]) },
      });

      // Return project with computed status
      return computeProjectStatus(updatedProject);
    }),
  deleteById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.project.delete({
        where: { id: input.id },
      });
    }),
  publishProject: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        locations: z.array(z.string()),
        sublocations: z.array(z.string()),
        startDate: z.string(),
        endDate: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const updatedProject = await ctx.db.project.update({
        where: { id: input.id },
        data: {
          startDate: new Date(input.startDate),
          endDate: new Date(input.endDate),
          status: PROJECT_STATUS.APPROVED, // Changed from PROJECT_STATUS.ACTIVE
          locations: { set: input.locations.map((id) => ({ id })) },
          sublocations: { set: input.sublocations.map((id) => ({ id })) },
        },
      });

      // Return project with computed status
      return computeProjectStatus(updatedProject);
    }),
  getActivityStats: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { todayStart, todayEnd } = getTodayStartAndEnd();

      const [
        activeProjectsCount,
        approvedProjectsCount,
        editingProjectsCount,
        completedProjectsCount,
        totalSlides,
        activeProjects,
        totalLocations,
        totalSublocations,
      ] = await Promise.all([
        // Count approved projects that are currently active (within date range)
        ctx.db.project.count({
          where: {
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            startDate: { lte: todayEnd },
            endDate: { gte: todayStart },
          },
        }),
        // Count approved projects that are not completed and not active
        ctx.db.project.count({
          where: {
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            OR: [
              // Projects with no start date (not active, not completed)
              { startDate: null },
              // Projects not yet started (future projects)
              { startDate: { gt: todayEnd } },
            ],
            // Exclude completed projects (past their end date)
            AND: [
              {
                OR: [
                  // Projects with no end date
                  { endDate: null },
                  // Projects not yet ended
                  { endDate: { gte: todayStart } },
                ],
              },
            ],
          },
        }),
        // Count projects that are currently being edited
        ctx.db.project.count({
          where: {
            organizationId: input.organizationId,
            status: PROJECT_STATUS.EDITING,
          },
        }),
        // Count approved projects that are completed (past their end date)
        ctx.db.project.count({
          where: {
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            endDate: { lt: todayStart },
          },
        }),
        // Count total slides across all projects
        ctx.db.slide.count({
          where: {
            project: {
              organizationId: input.organizationId,
              status: PROJECT_STATUS.APPROVED,
              startDate: { lte: todayEnd },
              endDate: { gte: todayStart },
            },
          },
        }),
        // Get active projects (approved + within date range)
        ctx.db.project.findMany({
          where: {
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            startDate: { lte: todayEnd },
            endDate: { gte: todayStart },
          },
          include: {
            locations: {
              select: { id: true },
            },
            sublocations: {
              select: { id: true },
            },
          },
        }),
        // Count total locations in the organization
        ctx.db.location.count({
          where: {
            organizationId: input.organizationId,
          },
        }),
        // Count total sublocations in the organization
        ctx.db.subLocation.count({
          where: {
            location: {
              organizationId: input.organizationId,
            },
          },
        }),
      ]);

      // Calculate unique locations and sublocations from active/approved projects
      const uniqueLocationIds = new Set<string>();
      const uniqueSublocationIds = new Set<string>();
      let totalLocationDeployments = 0;

      activeProjects.forEach((project) => {
        project.locations.forEach((location) => {
          uniqueLocationIds.add(location.id);
          totalLocationDeployments++;
        });
        project.sublocations.forEach((sublocation) => {
          uniqueSublocationIds.add(sublocation.id);
        });
      });

      const activeLocationsCount = uniqueLocationIds.size;
      const activeSublocationsCount = uniqueSublocationIds.size;
      const projectsWithLocations = activeProjects.filter(
        (p) => p.locations.length > 0 || p.sublocations.length > 0,
      ).length;

      // Calculate utilization rates
      const locationUtilizationRate =
        totalLocations > 0
          ? Math.round((activeLocationsCount / totalLocations) * 100)
          : 0;

      const sublocationUtilizationRate =
        totalSublocations > 0
          ? Math.round((activeSublocationsCount / totalSublocations) * 100)
          : 0;

      // Calculate average deployments per project
      const totalActiveAndApproved =
        activeProjectsCount + approvedProjectsCount;
      const averageLocationsPerProject =
        totalActiveAndApproved > 0
          ? Math.round(
              (totalLocationDeployments / totalActiveAndApproved) * 10,
            ) / 10
          : 0;

      return {
        activeProjectsCount,
        approvedProjectsCount,
        editingProjectsCount,
        completedProjectsCount,
        totalSlides,
        activeLocationsCount,
        activeSublocationsCount,
        totalLocations,
        totalSublocations,
        totalLocationDeployments,
        projectsWithLocations,
        locationUtilizationRate,
        sublocationUtilizationRate,
        averageLocationsPerProject,
        deploymentEfficiency:
          projectsWithLocations > 0
            ? Math.round((projectsWithLocations / totalActiveAndApproved) * 100)
            : 0,
      };
    }),
  getProjectActivityTimeline: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const [recentProjectActivity, projectStatusBreakdown, slidesGrowth] =
        await Promise.all([
          ctx.db.project.findMany({
            where: {
              organizationId: input.organizationId,
              updatedAt: { gte: thirtyDaysAgo },
            },
            select: {
              id: true,
              name: true,
              status: true,
              updatedAt: true,
              createdAt: true,
              startDate: true,
              endDate: true,
              _count: {
                select: {
                  slides: true,
                  locations: true,
                  sublocations: true,
                },
              },
            },
            orderBy: { updatedAt: "desc" },
            take: 10,
          }),
          ctx.db.project.groupBy({
            by: ["status"],
            where: { organizationId: input.organizationId },
            _count: { status: true },
          }),
          ctx.db.slide.count({
            where: {
              project: { organizationId: input.organizationId },
              createdAt: { gte: thirtyDaysAgo },
            },
          }),
        ]);

      // Compute dynamic status for recent activity
      const processedRecentActivity = computeProjectsStatus(
        recentProjectActivity,
      );

      return {
        recentActivity: processedRecentActivity,
        statusBreakdown: projectStatusBreakdown,
        slidesAddedThisMonth: slidesGrowth,
        activeDeployments: processedRecentActivity.filter(
          (p) =>
            p.status === PROJECT_STATUS.ACTIVE &&
            (p._count.locations > 0 || p._count.sublocations > 0),
        ).length,
      };
    }),
});
